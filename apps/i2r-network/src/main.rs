mod dev;
mod logging;

use std::{path::Path, time::Duration};

use anyhow::anyhow;
use clap::{Args, Parser, Subcommand};
use dev::identite_from_ads;
use infra::{
    dbus_executor::DbusHalAdapter,
    dns::{dummy::DummyDnsResolverAdapter, ppp_resolver::PppDnsResolverAdapter},
    dummy_ms::DummyMsAdapter,
    params::sqlite_params::SqliteParamsAdapter,
    pinger::{dummy::DummyPingerAdapter, icmp::IcmpPingerAdapter},
    random_generator::RandomGeneratorAdapter,
};
use network::{
    errors::NetworkError,
    manager::NetworkManager,
    ports::{HalPort, ParamsPort},
    retry::{retry_async_until_success, retry_until_success},
    watchdog::SystemdWatchdog,
};
use tracing::info;

use crate::dev::Identite;

const DEFAULT_DATABASE_PATH: &str = "/var/lib/i2r/i2r-params.db";

#[derive(Parser)]
#[command(
    name = "I2R Network",
    version = "0.1",
    about = "Gestionnaire de la connectivité du boitier AO3",
    long_about = None
)]
struct Cli {
    #[command(subcommand)]
    command: Commands,
}

#[derive(Subcommand)]
enum Commands {
    /// Se base sur des identifiants en dur pour se connecter
    Dev(StartArgs),
    Mock,
}

#[derive(Args)]
struct StartArgs {
    #[arg(short, long)]
    db_path: Option<String>,
}

#[tokio::main]
async fn main() {
    let _logger_guard = logging::setup();
    if let Err(error) = _logger_guard {
        println!("{:?}", error.to_string())
    }

    let cli = Cli::parse();

    info!("Connexion à la base de données en cours");

    let db_path = match &cli.command {
        Commands::Dev(args) => args
            .db_path
            .clone()
            .unwrap_or(DEFAULT_DATABASE_PATH.to_owned()),
        Commands::Mock => DEFAULT_DATABASE_PATH.to_owned(),
    };

    let params = SqliteParamsAdapter::connect(&db_path).expect("base de données injoignable");

    let configuration = params.fetch_configuration();

    info!("Récupération des identifiants en cours");
    let identite = retry_async_until_success(|| fetch_identite(), Duration::from_secs(2)).await;

    info!("Identifiants obtenus");
    info!("Connexion au HAL en cours");
    let dbus_hal = retry_until_success(|| DbusHalAdapter::new(), Duration::from_secs(2)).await;
    info!("Connexion au HAL réussie");

    let dummy_ms = DummyMsAdapter {
        radius_to_send: identite.radius.to_owned(),
    };

    let random_generator = RandomGeneratorAdapter;
    
    let watchdog = SystemdWatchdog::new(configuration.watchdog_interval_secs);
    tokio::spawn(async move {
        watchdog.start().await;
    });
    
    match &cli.command {
        Commands::Dev(_) => {
            let pinger = IcmpPingerAdapter {
                timeout: configuration.ping_timeout,
            };

            let dns_resolv_conf_parent_dir = Path::new("/etc/ppp");
            let ppp_dns = PppDnsResolverAdapter {
                path_to_resolv_conf_directory: dns_resolv_conf_parent_dir.to_owned(),
            };

            let mut network_manager = NetworkManager {
                hal: dbus_hal,
                module_securite: dummy_ms,
                pinger,
                random_generator,
                configuration,
                dns_resolver: ppp_dns,
                params,
            };

            network_manager.run().await;
        }
        Commands::Mock => {
            let pinger = DummyPingerAdapter {
                successful_ping: true,
            };

            let random_generator = RandomGeneratorAdapter;

            let dummy_dns = DummyDnsResolverAdapter {
                dns_value: configuration.pacy_ip.to_owned(),
            };

            let mut network_manager = NetworkManager {
                hal: dbus_hal,
                module_securite: dummy_ms,
                pinger,
                random_generator,
                configuration,
                dns_resolver: dummy_dns,
                params,
            };

            network_manager.run().await;
        }
    };
}

async fn fetch_identite() -> Result<Identite, NetworkError> {
    let dbus_hal = DbusHalAdapter::new()?;
    let ads = dbus_hal.get_ads().await?;

    identite_from_ads(&ads).ok_or(NetworkError::Other(anyhow!("ads introuvable")))
}
