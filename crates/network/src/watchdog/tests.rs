use super::*;
use googletest::{assert_that, prelude::*};
use std::os::unix::net::UnixDatagram;
use std::{env, time::Duration};
use tokio::time::{timeout, Instant};

fn setup_notify_socket(path: &str) {
    env::set_var(NOTIFY_SOCKET_NAME, path);
}

fn cleanup_notify_socket() {
    env::remove_var(NOTIFY_SOCKET_NAME);
}

#[test]
fn test_new_creates_watchdog_with_correct_interval() {
    let interval = Duration::from_secs(5);
    let watchdog = SystemdWatchdog::new(interval);

    assert_that!(watchdog.interval, eq(interval));
}

#[test]
fn test_all_message_types_with_no_socket() {
    // Ensure all message types work when no socket is configured
    cleanup_notify_socket();

    let watchdog = SystemdWatchdog::new(Duration::from_secs(1));

    // Test READY_MESSAGE
    let result = watchdog.send_notification(READY_MESSAGE);
    assert_that!(result, ok(anything()));

    // Test WATCHDOG_MESSAGE
    let result = watchdog.send_notification(WATCHDOG_MESSAGE);
    assert_that!(result, ok(anything()));

    // Test custom message
    let result = watchdog.send_notification("STATUS=Running");
    assert_that!(result, ok(anything()));
}

#[test]
fn test_send_notification_with_real_unix_socket() {
    // Use a temporary socket path in /tmp
    let socket_path = "/tmp/test_watchdog_socket";

    // Remove any existing socket file
    let _ = std::fs::remove_file(socket_path);

    // Create a Unix socket to receive messages
    let receiver = match UnixDatagram::bind(socket_path) {
        Ok(socket) => socket,
        Err(_) => {
            // Skip test if we can't create socket
            return;
        }
    };

    setup_notify_socket(socket_path);

    let watchdog = SystemdWatchdog::new(Duration::from_secs(1));
    let result = watchdog.send_notification(WATCHDOG_MESSAGE);

    assert_that!(result, ok(anything()));

    // Try to receive the message
    let mut buffer = [0u8; 1024];
    let received = receiver.recv(&mut buffer);

    match received {
        Ok(size) => {
            assert_that!(&buffer[..size], eq(WATCHDOG_MESSAGE.as_bytes()));
        }
        Err(_) => {
            // Socket might not receive immediately, but send should succeed
        }
    }

    cleanup_notify_socket();
    let _ = std::fs::remove_file(socket_path);
}

#[test]
fn test_send_notification_with_permission_denied_socket() {
    // Try to use a path that would cause permission denied
    setup_notify_socket("/root/restricted_socket");

    let watchdog = SystemdWatchdog::new(Duration::from_secs(1));
    let result = watchdog.send_notification(WATCHDOG_MESSAGE);

    // Should fail due to permission issues
    assert_that!(result, err(anything()));

    cleanup_notify_socket();
}

#[tokio::test]
async fn test_start_continues_on_heartbeat_failure() {
    // Set up a socket path that will cause failures
    setup_notify_socket("/tmp/non_existent_socket_for_failure_test");

    let watchdog = SystemdWatchdog::new(Duration::from_millis(50));

    // Start the watchdog and let it run for enough time to attempt multiple heartbeats
    let start_future = watchdog.start();
    let result = timeout(Duration::from_millis(200), start_future).await;

    // Should timeout because start() continues running even with failures
    assert_that!(result, err(anything()));

    cleanup_notify_socket();
}

#[tokio::test]
async fn test_watchdog_timing_precision() {
    cleanup_notify_socket();

    let interval = Duration::from_millis(100);
    let watchdog = SystemdWatchdog::new(interval);

    let start_time = Instant::now();
    let start_future = watchdog.start();

    // Let it run for approximately 2.5 intervals
    let result = timeout(Duration::from_millis(250), start_future).await;
    let elapsed = start_time.elapsed();

    // Should timeout and elapsed time should be close to 250ms with some tolerance
    assert_that!(result, err(anything()));
    assert_that!(elapsed.as_millis(), ge(240));
    assert_that!(elapsed.as_millis(), le(260));
}

#[tokio::test]
async fn test_concurrent_watchdog_instances() {
    cleanup_notify_socket();

    let watchdog1 = SystemdWatchdog::new(Duration::from_millis(50));
    let watchdog2 = SystemdWatchdog::new(Duration::from_millis(75));

    // Start both watchdogs concurrently
    let future1 = watchdog1.start();
    let future2 = watchdog2.start();

    let result = timeout(Duration::from_millis(200), async {
        tokio::select! {
            _ = future1 => {},
            _ = future2 => {},
        }
    })
    .await;

    // Should timeout because both start() methods run indefinitely
    assert_that!(result, err(anything()));
}
