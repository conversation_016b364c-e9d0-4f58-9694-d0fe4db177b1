#[cfg(test)]
mod tests;

use std::os::unix::net::{SocketAddr, UnixDatagram};
use std::{env, time::Duration};
use tracing::{debug, error, info, warn};

use std::os::linux::net::SocketAddrExt;

const NOTIFY_SOCKET_NAME: &str = "NOTIFY_SOCKET";
const READY_MESSAGE: &str = "READY=1";
const WATCHDOG_MESSAGE: &str = "WATCHDOG=1";

pub struct SystemdWatchdog {
    interval: Duration,
}

impl SystemdWatchdog {
     pub fn from_env(interval: Duration) -> Option<Self> {
        let socket_path = match env::var(NOTIFY_SOCKET_NAME) {
            Ok(path) => {
                debug!("Socket de notification systemd trouvé à : {}", path);
                path
            }
            Err(_) => {
                debug!(
                    "Socket de notification systemd non disponible ({} non défini)",
                    NOTIFY_SOCKET_NAME
                );
                return None;
            }
        };

        let socket = match UnixDatagram::unbound() {
            Ok(socket) => socket,
            Err(e) => {
                error!("Échec de création du socket Unix datagram : {}", e);
                return None;
            }
        };

        info!("Initialisation du watchdog systemd avec configuration par défaut");
        Some(Self {
            interval,
            health_monitor: None,
            socket,
            socket_path,
        })
    }


    pub async fn start(&self) {
        debug!("Démarrage du service watchdog systemd");

        // Send initial ready signal
        if let Err(e) = self.send_ready_signal() {
            warn!("Échec de l'envoi du signal READY initial à systemd : {}", e);
        } else {
            info!("Signal READY envoyé avec succès à systemd");
        }

        // Start heartbeat loop
        loop {
            debug!("Envoi du heartbeat watchdog");
            if let Err(e) = self.send_heartbeat() {
                warn!("Échec de l'envoi du heartbeat watchdog : {}", e);
            } else {
                debug!("Heartbeat watchdog envoyé avec succès");
            }

            tokio::time::sleep(self.interval).await;
        }
    }

    fn send_heartbeat(&self) -> Result<(), std::io::Error> {
        self.send_notification(WATCHDOG_MESSAGE)
    }

    fn send_ready_signal(&self) -> Result<(), std::io::Error> {
        self.send_notification(READY_MESSAGE)
    }

    fn send_notification(&self, message: &str) -> Result<(), std::io::Error> {
        let socket_path = match env::var(NOTIFY_SOCKET_NAME) {
            Ok(path) => {
                debug!(
                    "Socket de notification systemd trouvé à l'emplacement : {}",
                    path
                );
                path
            }
            Err(_) => {
                debug!(
                    "Socket de notification systemd non disponible ({} non défini)",
                    NOTIFY_SOCKET_NAME
                );
                return Ok(());
            }
        };

        let socket = UnixDatagram::unbound().map_err(|e| {
            error!("Échec de la création du socket datagram Unix : {}", e);
            e
        })?;

        let message_bytes = message.as_bytes();

        // Send message to socket (handle both abstract and file-based sockets)
        let result = {
            if socket_path.starts_with('@') {
                let abstract_name = &socket_path[1..]; // Remove the '@' prefix
                let addr = SocketAddr::from_abstract_name(abstract_name.as_bytes())?;
                socket.connect_addr(&addr)?;
                socket.send(message_bytes)
            } else {
                socket.send_to(message_bytes, &socket_path)
            }
        };

        result.map_err(|e| {
            error!(
                "Échec de l'envoi de la notification au socket systemd '{}' : {}",
                socket_path, e
            );
            e
        })?;

        debug!("Notification envoyée avec succès à systemd");
        Ok(())
    }
}
