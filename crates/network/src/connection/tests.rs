use std::{net::Ipv4Addr, time::Duration};

use mockall::{predicate, Sequence};
use tokio::time::timeout;

use crate::{
    configuration::{default_configuration, NetworkConfiguration},
    connection::<PERSON><PERSON>and<PERSON>,
    credentials::Credentials,
    datacenter::Datacenter,
    tests::{<PERSON>ckHalPort, Mock<PERSON>ingerPort, MockRandomGeneratorPort},
};

#[tokio::test(start_paused = true)]
async fn la_connection_est_verifiee_a_chaque_intervalle() {
    let mut pinger = MockPingerPort::new();
    pinger.expect_is_ip_reachable().times(2).returning(|_| true);

    let mut opts = default_test_configuration();
    opts.check_interval = Duration::from_secs(2);

    let connection_handler = ConnectionHandler::new(
        pinger,
        always_zero_random_generator(),
        mocked_hal(),
        default_credentials(),
        opts.clone(),
    );

    let _ = timeout(Duration::from_secs(2), connection_handler.watch()).await;
}

#[tokio::test(start_paused = true)]
async fn le_ping_est_reessaye_tant_qu_il_echoue() {
    let mut opts = default_test_configuration();
    opts.check_interval = Duration::from_secs(10);
    opts.ping_interval = Duration::from_secs(2);

    let mut sequence = Sequence::new();
    let mut pinger = MockPingerPort::new();

    // On fait en sorte que le SI ne soit pas joignable deux fois,
    // puis sera joignable la troisième fois
    pinger
        .expect_is_ip_reachable()
        .times(2)
        .in_sequence(&mut sequence)
        .returning(|_| false);
    pinger
        .expect_is_ip_reachable()
        .times(1)
        .in_sequence(&mut sequence)
        .returning(|_| true);

    let connection_handler = ConnectionHandler::new(
        pinger,
        always_zero_random_generator(),
        mocked_hal(),
        default_credentials(),
        opts,
    );
    let _ = timeout(Duration::from_secs(12), connection_handler.watch()).await;
}

#[tokio::test(start_paused = true)]
async fn un_delai_aleatoire_est_ajoute_apres_chaque_ping() {
    let mut opts = default_test_configuration();
    opts.ping_interval = Duration::from_secs(10);
    opts.maximum_random_ping_delay = Duration::from_secs(2);

    let mut sequence = Sequence::new();
    let mut random_generator = MockRandomGeneratorPort::new();
    random_generator
        .expect_between()
        .with(predicate::eq(0..2000))
        .times(1)
        .in_sequence(&mut sequence)
        .return_const(500u64);

    random_generator
        .expect_between()
        .with(predicate::eq(0..2000))
        .times(2)
        .in_sequence(&mut sequence)
        .return_const(1800u64);

    let connection_handler = ConnectionHandler::new(
        pinger_with_si_not_reachable(),
        random_generator,
        mocked_hal(),
        default_credentials(),
        opts,
    );

    let _ = timeout(Duration::from_millis(22300), connection_handler.watch()).await;
}

// #[ignore = "à implémenter"]
// #[tokio::test(start_paused = true)]
// async fn le_ping_est_considere_comme_echoue_lors_d_un_timeout() {
//     todo!("A implémenter lorsque la version de Rust supportera les async closure")
// }

#[tokio::test(start_paused = true)]
async fn un_reset_modem_est_demande_lorsque_la_limite_d_echecs_de_ping_est_atteinte() {
    let mut opts = default_test_configuration();
    opts.ping_failures_limit_before_modem_reset = 3;
    opts.waiting_delay_after_modem_reset = Duration::ZERO;
    opts.ping_interval = Duration::from_secs(2);

    let mut hal = MockHalPort::new();
    hal.expect_reboot_modem().once().returning(|| Ok(()));
    hal.expect_connect().once().returning(|_, _, _| Ok(()));

    let connection_handler = ConnectionHandler::new(
        pinger_with_si_not_reachable(),
        always_zero_random_generator(),
        hal,
        default_credentials(),
        opts,
    );

    let _ = timeout(Duration::from_secs(12), connection_handler.watch()).await;
}

#[tokio::test(start_paused = true)]
async fn apres_un_reset_modem_un_delai_est_attendu_avant_de_reprendre() {
    let mut opts = default_test_configuration();
    opts.ping_failures_limit_before_modem_reset = 3;
    opts.ping_interval = Duration::from_secs(2);
    opts.waiting_delay_after_modem_reset = Duration::from_secs(10);

    let mut sequence = Sequence::new();
    let mut hal = MockHalPort::new();
    let mut pinger = MockPingerPort::new();
    pinger
        .expect_is_ip_reachable()
        .times(3)
        .in_sequence(&mut sequence)
        .return_const(false);
    pinger
        .expect_is_ip_reachable()
        .times(3)
        .in_sequence(&mut sequence)
        .return_const(false);
    hal.expect_reboot_modem()
        .once()
        .in_sequence(&mut sequence)
        .returning(|| Ok(()));
    hal.expect_connect()
        .once()
        .in_sequence(&mut sequence)
        .returning(|_, _, _| Ok(()));
    pinger
        .expect_is_ip_reachable()
        .times(3)
        .in_sequence(&mut sequence)
        .return_const(false);
    pinger
        .expect_is_ip_reachable()
        .times(1)
        .in_sequence(&mut sequence)
        .return_const(true);

    let connection_handler = ConnectionHandler::new(
        pinger,
        always_zero_random_generator(),
        hal,
        default_credentials(),
        opts,
    );

    let _ = timeout(Duration::from_secs(30), connection_handler.watch()).await;
}

#[tokio::test(start_paused = true)]
async fn quand_le_datacenter_principal_est_injoignable_le_secondaire_est_contacte() {
    let mut opts = default_test_configuration();
    opts.ping_interval = Duration::from_secs(10);
    opts.ping_failures_limit_before_modem_reset = 2;
    opts.waiting_delay_after_modem_reset = Duration::from_secs(45);
    opts.check_interval = Duration::ZERO;
    opts.pacy_ip = Ipv4Addr::new(10, 139, 48, 3);
    opts.noe_ip = Ipv4Addr::new(10, 139, 17, 20);
    opts.primary_datacenter = Some(Datacenter::PACY);
    opts.datacenter_netmask = Ipv4Addr::new(255, 255, 255, 0);

    let mut sequence = Sequence::new();

    let mut pinger = MockPingerPort::new();

    pinger
        .expect_is_ip_reachable()
        .with(mockall::predicate::eq(Ipv4Addr::new(10, 139, 48, 3)))
        .times(2)
        .in_sequence(&mut sequence)
        .return_const(false);

    pinger
        .expect_is_ip_reachable()
        .with(mockall::predicate::eq(Ipv4Addr::new(10, 139, 17, 20)))
        .times(2)
        .in_sequence(&mut sequence)
        .return_const(false);

    let mut hal = MockHalPort::new();
    hal.expect_reboot_modem()
        .once()
        .in_sequence(&mut sequence)
        .returning(|| Ok(()));

    let connection_handler = ConnectionHandler::new(
        pinger,
        always_zero_random_generator(),
        hal,
        default_credentials(),
        opts,
    );

    let _ = timeout(Duration::from_secs(45), connection_handler.watch()).await;
}

#[tokio::test(start_paused = true)]
async fn un_reboot_boitier_est_demande_quand_la_limite_de_resets_modem_a_ete_atteinte() {
    let mut opts = default_test_configuration();
    opts.ping_failures_limit_before_modem_reset = 3;
    opts.modem_resets_before_bip_reboot = 2;
    opts.ping_interval = Duration::from_secs(2);
    opts.waiting_delay_after_modem_reset = Duration::from_secs(10);

    let mut sequence = Sequence::new();
    let mut hal = MockHalPort::new();
    let mut pinger = MockPingerPort::new();
    pinger
        .expect_is_ip_reachable()
        .times(6)
        .in_sequence(&mut sequence)
        .return_const(false);
    hal.expect_reboot_modem()
        .once()
        .in_sequence(&mut sequence)
        .returning(|| Ok(()));
    hal.expect_connect()
        .once()
        .in_sequence(&mut sequence)
        .returning(|_, _, _| Ok(()));
    pinger
        .expect_is_ip_reachable()
        .times(6)
        .in_sequence(&mut sequence)
        .return_const(false);
    hal.expect_reboot_modem()
        .once()
        .in_sequence(&mut sequence)
        .returning(|| Ok(()));
    hal.expect_connect()
        .once()
        .in_sequence(&mut sequence)
        .returning(|_, _, _| Ok(()));
    hal.expect_reboot_bip()
        .once()
        .in_sequence(&mut sequence)
        .returning(|| Ok(()));

    let connection_handler = ConnectionHandler::new(
        pinger,
        always_zero_random_generator(),
        hal,
        default_credentials(),
        opts,
    );

    let _ = timeout(Duration::from_secs(54), connection_handler.watch()).await;
}

fn mocked_hal() -> MockHalPort {
    let mut mock = MockHalPort::new();

    mock.expect_reboot_modem().returning(|| Ok(()));
    mock.expect_connect().returning(|_, _, _| Ok(()));

    mock
}

fn always_zero_random_generator() -> MockRandomGeneratorPort {
    let mut mock = MockRandomGeneratorPort::new();
    mock.expect_between().return_const(0u32);

    mock
}

fn pinger_with_si_not_reachable() -> MockPingerPort {
    let mut mock = MockPingerPort::new();
    mock.expect_is_ip_reachable().returning(|_| false);

    mock
}

fn default_credentials() -> Credentials {
    Credentials {
        login: "login".to_owned(),
        apn: "apn".to_owned(),
        password: "pass".to_owned(),
    }
}

fn default_test_configuration() -> NetworkConfiguration {
    let mut conf = default_configuration();
    conf.primary_datacenter = Some(Datacenter::PACY);

    return conf;
}
